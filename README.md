# تطبيق تغيير خلفية سطح المكتب

تطبيق macOS يعمل في شريط القوائم لتغيير خلفية سطح المكتب بسهولة.

## المميزات

- **أيقونة في شريط القوائم**: يظهر التطبيق كأيقونة في شريط القوائم العلوي
- **نافذة منبثقة أنيقة**: عند الضغط على الأيقونة تظهر نافذة بحواف مدورة
- **قائمة الزر الأيمن**: الضغط بالزر الأيمن على الأيقونة يظهر قائمة لإنهاء التطبيق
- **تخطيط موحد**:
  - 10 مربعات كبيرة بنفس الحجم (180×120 بكسل)
  - مرتبة في 5 صفوف، مربعان في كل صف
- **معاينة الصور**: بعد اختيار الصورة تظهر كمعاينة داخل المربع
- **تطبيق بخطوتين**:
  1. اختيار الصورة من الجهاز (تظهر في المربع)
  2. الضغط على المربع مرة أخرى لتطبيق الخلفية
- **دعم أنواع ملفات متعددة**: JPG, PNG, HEIC, TIFF, BMP, GIF, WebP
- **إشعارات حديثة**: إشعارات تأكيد عند تطبيق الخلفية بنجاح

## كيفية الاستخدام

1. **تشغيل التطبيق**: شغل التطبيق وستظهر أيقونة في شريط القوائم
2. **فتح النافذة**: اضغط بالزر الأيسر على الأيقونة لفتح نافذة اختيار الخلفيات
3. **اختيار صورة**: اضغط على أي مربع فارغ لفتح نافذة اختيار الملفات
4. **معاينة الصورة**: بعد اختيار الصورة ستظهر كمعاينة داخل المربع
5. **تطبيق الخلفية**: اضغط على المربع مرة أخرى لتطبيق الصورة كخلفية لسطح المكتب
6. **إنهاء التطبيق**: اضغط بالزر الأيمن على الأيقونة واختر "إنهاء التطبيق"

## أنواع الملفات المدعومة

التطبيق يدعم جميع أنواع ملفات الصور الشائعة:
- **JPG/JPEG**: الصور المضغوطة
- **PNG**: الصور بخلفية شفافة
- **HEIC**: تنسيق Apple الحديث
- **TIFF**: الصور عالية الجودة
- **BMP**: الصور غير المضغوطة
- **GIF**: الصور المتحركة (الإطار الأول فقط)
- **WebP**: تنسيق Google الحديث

## المتطلبات

- macOS 12.0 أو أحدث
- Xcode 14.0 أو أحدث للتطوير

## البناء والتشغيل

```bash
# بناء التطبيق
xcodebuild -project wall.xcodeproj -scheme wall -configuration Debug build

# تشغيل التطبيق
open /path/to/build/wall.app
```

## الصلاحيات

التطبيق يحتاج إلى الصلاحيات التالية:
- قراءة مجلد الصور
- قراءة خلفيات النظام
- تغيير خلفية سطح المكتب

## الملاحظات

- التطبيق لا يظهر في الـ Dock (يعمل في الخلفية)
- يمكن إنهاء التطبيق بالضغط بالزر الأيمن على الأيقونة واختيار "إنهاء التطبيق"
- جميع المربعات العشرة لها نفس الحجم الكبير للسهولة في الاستخدام
- **المربع الفارغ**: يظهر أيقونة "+" ويفتح نافذة اختيار الصور عند الضغط عليه
- **المربع مع صورة**: يظهر معاينة الصورة ويطبقها كخلفية عند الضغط عليه
- يمكن تغيير الصورة في أي مربع بالضغط عليه مرة أخرى
- اختصار لوحة المفاتيح: Cmd+Q لإنهاء التطبيق من القائمة

## التطوير

الملفات الرئيسية:
- `wallApp.swift`: نقطة دخول التطبيق وإعداد شريط القوائم
- `ContentView.swift`: واجهة المستخدم وشبكة الخلفيات
- `wall.entitlements`: صلاحيات التطبيق
- `Info.plist`: إعدادات التطبيق

---

تم تطوير هذا التطبيق باستخدام SwiftUI و AppKit لنظام macOS.
