import SwiftUI
import AppKit
import UserNotifications
import UniformTypeIdentifiers
import Foundation

struct WallpaperGridView: View {
    @State private var isLoading = false
    @State private var wallpaperBoxes: [WallpaperBox] = []


    let columns = [
        GridItem(.flexible()),
        GridItem(.flexible())
    ]

    var body: some View {

        ScrollView(.vertical, showsIndicators: false) {
            LazyVGrid(columns: columns, spacing: 10) {
                ForEach(wallpaperBoxes, id: \.id) { wallpaperBox in
                    WallpaperSelectionBox(wallpaperBox: wallpaperBox)
                }
            }
            .padding(10)
        }
        .frame(width: 395, height: 270)
        .background(.clear)
        .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 35))
        .clipShape(RoundedRectangle(cornerRadius: 35))
        .onAppear {
            initializeWallpaperBoxes()
        }
    }

    private func initializeWallpaperBoxes() {
        if wallpaperBoxes.isEmpty {
            wallpaperBoxes = (1...10).map { index in
                let wallpaperBox = WallpaperBox(
                    id: index,
                    title: "خلفية \(index)"
                )



                return wallpaperBox
            }
        }
    }
}

class WallpaperBox: ObservableObject {
    let id: Int
    let title: String
    @Published var selectedImage: NSImage?
    var imagePath: String? // للاستخدام في تطبيق الخلفية

    init(id: Int, title: String) {
        self.id = id
        self.title = title
        self.selectedImage = nil
        self.imagePath = nil

        loadSavedImage()
    }

    func setImage(_ image: NSImage, path: String) {
        self.selectedImage = image
        self.imagePath = path

        saveImageData(image: image, path: path)
    }

    func clearImage() {
        self.selectedImage = nil
        self.imagePath = nil

        clearSavedData()
    }


    private func saveImageData(image: NSImage, path: String) {
        let userDefaults = UserDefaults.standard

        userDefaults.set(path, forKey: "wallpaper_path_\(id)")


        let optimizedImage = optimizeImageForDisplay(image)

        if let tiffData = optimizedImage.tiffRepresentation,
           let bitmapImage = NSBitmapImageRep(data: tiffData),
           let pngData = bitmapImage.representation(using: .png, properties: [
               .compressionFactor: 0.8
           ]) {
            userDefaults.set(pngData, forKey: "wallpaper_image_\(id)")
        }

        userDefaults.synchronize()
    }

    private func loadSavedImage() {
        let userDefaults = UserDefaults.standard


        if let savedPath = userDefaults.string(forKey: "wallpaper_path_\(id)") {
            self.imagePath = savedPath

            if let image = NSImage(contentsOfFile: savedPath) {
                self.selectedImage = image
                return
            }

            if let savedImageData = userDefaults.data(forKey: "wallpaper_image_\(id)"),
               let image = NSImage(data: savedImageData) {
                self.selectedImage = image
            }
        }
    }

    private func clearSavedData() {
        let userDefaults = UserDefaults.standard
        userDefaults.removeObject(forKey: "wallpaper_path_\(id)")
        userDefaults.removeObject(forKey: "wallpaper_image_\(id)")
        userDefaults.synchronize()
    }

    // تحسين جودة الصورة للعرض
    private func optimizeImageForDisplay(_ image: NSImage) -> NSImage {
        let optimizedImage = NSImage(size: NSSize(width: 180, height: 120))
        optimizedImage.lockFocus()

        // استخدام إعدادات عالية الجودة
        let context = NSGraphicsContext.current
        context?.imageInterpolation = .high
        context?.shouldAntialias = true
        context?.compositingOperation = .copy

        // تحسين جودة الرسم
        if let cgContext = context?.cgContext {
            cgContext.interpolationQuality = .high
            cgContext.setShouldAntialias(true)
            cgContext.setShouldSmoothFonts(true)
        }

        // رسم الصورة بجودة عالية
        image.draw(in: NSRect(origin: .zero, size: NSSize(width: 180, height: 120)),
                   from: NSRect(origin: .zero, size: image.size),
                   operation: .copy,
                   fraction: 1.0)

        optimizedImage.unlockFocus()
        return optimizedImage
    }
}

struct WallpaperSelectionBox: View {
    @ObservedObject var wallpaperBox: WallpaperBox
    @State private var isHovered = false

    var body: some View {
        Button(action: {
            if wallpaperBox.selectedImage != nil {
                applyWallpaper()
            } else {
                chooseImageFromDevice()
            }
        }) {
            ZStack {
                RoundedRectangle(cornerRadius: 30)
                    .fill(Color.white.opacity(0.15))
                    .frame(width: 180, height: 120)

                if let selectedImage = wallpaperBox.selectedImage {
                    // عرض الصورة المختارة بجودة عالية
                    Image(nsImage: selectedImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 180, height: 120)
                        .clipped()
                        .cornerRadius(30)
                        .drawingGroup() // تحسين الأداء والجودة
                } else {
                    // مربع فارغ مع أيقونة واحدة حسب موقع المربع
                    VStack {
                        if wallpaperBox.id % 2 == 1 {
                            // المربعات الفردية (العمود الأيسر) - أيقونة النهار
                            Image(systemName: "cloud.sun.fill")
                                .font(.system(size: 20))
                                .foregroundColor(.white)
                        } else {
                            // المربعات الزوجية (العمود الأيمن) - أيقونة الليل
                            Image(systemName: "cloud.moon.fill")
                                .font(.system(size: 20))
                                .foregroundColor(.white)
                        }
                    }
                }

                // تأثير التمرير
                RoundedRectangle(cornerRadius: 30)
                    .stroke(Color.white.opacity(0.5), lineWidth: isHovered ? 2 : 0)
                    .frame(width: 180, height: 120)
                    .animation(.easeInOut(duration: 0.2), value: isHovered)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .onHover { hovering in
            isHovered = hovering
        }
        .help("")
        .contextMenu {
            if wallpaperBox.selectedImage != nil {
                Button("حذف الصورة") {
                    wallpaperBox.clearImage()
                }

                Button("اختيار صورة جديدة") {
                    chooseImageFromDevice()
                }

                Divider()

                Button("تطبيق كخلفية") {
                    applyWallpaper()
                }
            }
        }
    }

    private func chooseImageFromDevice() {
        let openPanel = NSOpenPanel()
        openPanel.title = "اختر صورة خلفية"
        openPanel.canChooseFiles = true
        openPanel.canChooseDirectories = false
        openPanel.allowsMultipleSelection = false

        if #available(macOS 12.0, *) {
            openPanel.allowedContentTypes = [.jpeg, .png, .heic, .tiff, .bmp, .gif, .webP]
        } else {
            openPanel.allowedFileTypes = ["jpg", "jpeg", "png", "heic", "tiff", "bmp", "gif", "webp"]
        }

        openPanel.level = NSWindow.Level.floating
        openPanel.orderFrontRegardless()
        NSApp.activate(ignoringOtherApps: true)

        openPanel.begin { response in
            if response == .OK, let url = openPanel.url {
                if let image = NSImage(contentsOfFile: url.path) {
                    // استخدام الطريقة الجديدة لحفظ الصورة والمسار
                    wallpaperBox.setImage(image, path: url.path)
                }
            }
        }
    }

    private func applyWallpaper() {
        guard let imagePath = wallpaperBox.imagePath else { return }

        // التحقق من وجود الملف في المسار الأصلي
        if FileManager.default.fileExists(atPath: imagePath) {
            WallpaperHelper.setImageWallpaper(imagePath)
        } else {
            // إذا لم يعد الملف موجوداً، إنشاء ملف مؤقت من الصورة المحفوظة
            if let image = wallpaperBox.selectedImage {
                createTemporaryImageFile(from: image) { tempPath in
                    if let tempPath = tempPath {
                        WallpaperHelper.setImageWallpaper(tempPath)
                        // تحديث المسار إلى الملف المؤقت
                        wallpaperBox.imagePath = tempPath
                    }
                }
            }
        }
    }

    private func createTemporaryImageFile(from image: NSImage, completion: @escaping (String?) -> Void) {
        DispatchQueue.global(qos: .userInitiated).async {
            let tempDirectory = NSTemporaryDirectory()
            let fileName = "wallpaper_\(UUID().uuidString).png"
            let tempPath = tempDirectory + fileName

            if let tiffData = image.tiffRepresentation,
               let bitmapImage = NSBitmapImageRep(data: tiffData),
               let pngData = bitmapImage.representation(using: .png, properties: [:]) {

                do {
                    try pngData.write(to: URL(fileURLWithPath: tempPath))
                    DispatchQueue.main.async {
                        completion(tempPath)
                    }
                } catch {
                    print("خطأ في إنشاء الملف المؤقت: \(error)")
                    DispatchQueue.main.async {
                        completion(nil)
                    }
                }
            } else {
                DispatchQueue.main.async {
                    completion(nil)
                }
            }
        }
    }
}

// امتداد لتغيير حجم الصورة بجودة عالية
extension NSImage {
    func resized(to newSize: NSSize) -> NSImage {
        let newImage = NSImage(size: newSize)
        newImage.lockFocus()

        // إعدادات عالية الجودة
        let context = NSGraphicsContext.current
        context?.imageInterpolation = .high
        context?.shouldAntialias = true
        context?.compositingOperation = .copy

        // تحسين جودة الرسم
        NSGraphicsContext.current?.cgContext.interpolationQuality = .high

        self.draw(in: NSRect(origin: .zero, size: newSize),
                  from: NSRect(origin: .zero, size: self.size),
                  operation: .copy,
                  fraction: 1.0)

        newImage.unlockFocus()
        return newImage
    }
}

// مساعد لتطبيق الخلفيات
class WallpaperHelper {
    static func setImageWallpaper(_ imagePath: String) {
        guard let mainScreen = NSScreen.main else {
            print("خطأ: لا يمكن العثور على الشاشة الرئيسية")
            return
        }

        let url = URL(fileURLWithPath: imagePath)

        do {
            try NSWorkspace.shared.setDesktopImageURL(url, for: mainScreen, options: [:])

            // تنظيف الملفات المؤقتة القديمة
            cleanupOldTemporaryFiles()

            // إظهار إشعار نجاح
            DispatchQueue.main.async {
                showNotification(title: "تم تغيير الخلفية", message: "تم تطبيق الخلفية الجديدة بنجاح")
            }
        } catch {
            print("خطأ في تطبيق الخلفية: \(error)")

            // إظهار إشعار خطأ
            DispatchQueue.main.async {
                showNotification(title: "خطأ", message: "فشل في تطبيق الخلفية: \(error.localizedDescription)")
            }
        }
    }

    // تنظيف الملفات المؤقتة القديمة
    private static func cleanupOldTemporaryFiles() {
        DispatchQueue.global(qos: .utility).async {
            let tempDirectory = NSTemporaryDirectory()
            let fileManager = FileManager.default

            do {
                let tempContents = try fileManager.contentsOfDirectory(atPath: tempDirectory)
                let wallpaperFiles = tempContents.filter { $0.hasPrefix("wallpaper_") && $0.hasSuffix(".png") }

                for fileName in wallpaperFiles {
                    let filePath = tempDirectory + fileName
                    let fileURL = URL(fileURLWithPath: filePath)

                    // حذف الملفات الأقدم من ساعة واحدة
                    if let attributes = try? fileManager.attributesOfItem(atPath: filePath),
                       let creationDate = attributes[.creationDate] as? Date,
                       Date().timeIntervalSince(creationDate) > 3600 {
                        try? fileManager.removeItem(at: fileURL)
                    }
                }
            } catch {
                print("خطأ في تنظيف الملفات المؤقتة: \(error)")
            }
        }
    }

    static func showNotification(title: String, message: String) {
        let center = UNUserNotificationCenter.current()

        // طلب إذن الإشعارات
        center.requestAuthorization(options: [.alert, .sound]) { granted, error in
            if granted {
                let content = UNMutableNotificationContent()
                content.title = title
                content.body = message
                content.sound = .default

                let request = UNNotificationRequest(
                    identifier: UUID().uuidString,
                    content: content,
                    trigger: nil
                )

                center.add(request) { error in
                    if let error = error {
                        print("خطأ في إرسال الإشعار: \(error)")
                    }
                }
            }
        }
    }
}
