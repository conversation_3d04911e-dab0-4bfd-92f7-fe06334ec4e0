//
//  wallApp.swift
//  wall
//
//  Created by <PERSON><PERSON> on 27/07/2025.
//

import SwiftUI
import AppKit
import Cocoa
import ObjectiveC

@main
struct wallApp: App {
    @NSApplicationDelegateAdaptor(AppDelegate.self) var appDelegate

    var body: some Scene {
        Settings {
            EmptyView()
        }
    }
}

class AppDelegate: NSObject, NSApplicationDelegate {
    var statusItem: NSStatusItem?
    var wallpaperWindow: NSWindow?
    var statusBarMenu: NSMenu?
    var mouseMonitor: Any?
    var wallpaperView: WallpaperGridView?
    var originalPresentationOptions: NSApplication.PresentationOptions = []

    func applicationDidFinishLaunching(_ notification: Notification) {

        NSApp.setActivationPolicy(.accessory)

        // حفظ الحالة الأصلية لشريط القوائم عند بدء التطبيق
        originalPresentationOptions = NSApp.presentationOptions

        statusItem = NSStatusBar.system.statusItem(withLength: NSStatusItem.squareLength)

        if let button = statusItem?.button {
            button.image = NSImage(systemSymbolName: "photo.on.rectangle", accessibilityDescription: "Wallpaper Changer")
            button.action = #selector(handleStatusBarClick(_:))
            button.target = self
            button.sendAction(on: [.leftMouseUp, .rightMouseUp])
        }

        prepareWallpaperWindow()

        createStatusBarMenu()
    }

    private func createStatusBarMenu() {
        statusBarMenu = NSMenu()

        let quitMenuItem = NSMenuItem(
            title: "Quit",
            action: #selector(quitApplication),
            keyEquivalent: "q"
        )
        quitMenuItem.target = self
        statusBarMenu?.addItem(quitMenuItem)
    }

    @objc func handleStatusBarClick(_ sender: NSStatusBarButton) {
        let event = NSApp.currentEvent!

        if event.type == .rightMouseUp {
            if let menu = statusBarMenu {
                statusItem?.menu = menu
                statusItem?.button?.performClick(nil)
                statusItem?.menu = nil
            }
        } else {
            toggleWallpaperWindow()
        }
    }

    func toggleWallpaperWindow() {
        if let window = wallpaperWindow, window.isVisible {
            hideWallpaperWindow()
        } else {
            showWallpaperWindow()
        }
    }


    func prepareWallpaperWindow() {

        wallpaperView = WallpaperGridView()
        let contentView = NSHostingController(rootView: wallpaperView!).view

        let window = createWindow(contentView: contentView)
        window.alphaValue = 0.0

        self.wallpaperWindow = window
    }

    func showWallpaperWindow() {
        guard let window = wallpaperWindow else { return }

        // إذا كانت النافذة مرئية بالفعل، لا تفعل شيئاً
        if window.isVisible { return }

        let windowSize = NSSize(width: 395, height: 270)

        // إجبار إظهار شريط القوائم وعدم إخفاؤه
        NSApp.presentationOptions = []

        // تحديد موقع النافذة بالقرب من زر شريط الحالة
        if let button = statusItem?.button {
            let buttonFrame = button.window?.convertToScreen(button.frame) ?? NSRect.zero
            let x = buttonFrame.midX - windowSize.width / 2
            let y = buttonFrame.minY - windowSize.height - 10  // زيادة المسافة من 10 إلى 18
            window.setFrameOrigin(NSPoint(x: x, y: y))


        }

        // إظهار النافذة فوراً
        window.alphaValue = 1.0
        NSApp.activate(ignoringOtherApps: true)
        window.makeKeyAndOrderFront(nil)
        window.orderFrontRegardless()
    }

    func hideWallpaperWindow() {
        guard let window = wallpaperWindow else { return }
        window.orderOut(nil)



        // إعادة الحالة الأصلية لشريط القوائم
        NSApp.presentationOptions = originalPresentationOptions
    }

    func createWindow(contentView: NSView) -> NSWindow {
        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 395, height: 270),
            styleMask: [.borderless],
            backing: .buffered,
            defer: false
        )

        // Window settings
        window.backgroundColor = NSColor.clear
        window.isOpaque = false
        window.hasShadow = false
        window.titlebarAppearsTransparent = true
        window.titleVisibility = .hidden
        window.styleMask.remove(.titled)
        window.level = .floating
        window.collectionBehavior = [.canJoinAllSpaces, .fullScreenAuxiliary]

        // Add content
        window.contentView = contentView

        // Add observer to hide window when it loses focus
        NotificationCenter.default.addObserver(
            forName: NSWindow.didResignKeyNotification,
            object: window,
            queue: .main
        ) { [weak self] _ in
            DispatchQueue.main.async {
                self?.hideWallpaperWindow()
            }
        }

        // Add global mouse monitor to hide window on outside clicks
        mouseMonitor = NSEvent.addGlobalMonitorForEvents(matching: [.leftMouseDown, .rightMouseDown]) { [weak self] event in
            guard let self = self, let window = self.wallpaperWindow else { return }

            // التحقق من أن النقرة خارج النافذة
            let screenLocation = NSEvent.mouseLocation

            if !window.frame.contains(screenLocation) && window.isVisible {
                DispatchQueue.main.async {
                    self.hideWallpaperWindow()
                }
            }
        }

        return window
    }

    func closeWallpaperWindow() {
        // هذه الدالة تُستخدم فقط عند إنهاء التطبيق
        guard let window = wallpaperWindow else { return }

        // Remove global mouse monitor
        if let monitor = mouseMonitor {
            NSEvent.removeMonitor(monitor)
            mouseMonitor = nil
        }

        // Remove notification observers
        NotificationCenter.default.removeObserver(self, name: NSWindow.didResignKeyNotification, object: window)

        // Close window completely
        window.close()
        wallpaperWindow = nil
        wallpaperView = nil
    }


    @objc func quitApplication() {
        NSApplication.shared.terminate(nil)
    }
}
