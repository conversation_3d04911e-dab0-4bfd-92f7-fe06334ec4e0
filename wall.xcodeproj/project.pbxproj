// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		45A9FD172E3691E000E27F92 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 45A9FD002E3691E000E27F92 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 45A9FD072E3691E000E27F92;
			remoteInfo = wall;
		};
		45A9FD212E3691E000E27F92 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 45A9FD002E3691E000E27F92 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 45A9FD072E3691E000E27F92;
			remoteInfo = wall;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		45A9FD082E3691E000E27F92 /* wall.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = wall.app; sourceTree = BUILT_PRODUCTS_DIR; };
		45A9FD162E3691E000E27F92 /* wallTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = wallTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		45A9FD202E3691E000E27F92 /* wallUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = wallUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		45A9FD0A2E3691E000E27F92 /* wall */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = wall;
			sourceTree = "<group>";
		};
		45A9FD192E3691E000E27F92 /* wallTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = wallTests;
			sourceTree = "<group>";
		};
		45A9FD232E3691E000E27F92 /* wallUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = wallUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		45A9FD052E3691E000E27F92 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		45A9FD132E3691E000E27F92 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		45A9FD1D2E3691E000E27F92 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		45A9FCFF2E3691E000E27F92 = {
			isa = PBXGroup;
			children = (
				45A9FD0A2E3691E000E27F92 /* wall */,
				45A9FD192E3691E000E27F92 /* wallTests */,
				45A9FD232E3691E000E27F92 /* wallUITests */,
				45A9FD092E3691E000E27F92 /* Products */,
			);
			sourceTree = "<group>";
		};
		45A9FD092E3691E000E27F92 /* Products */ = {
			isa = PBXGroup;
			children = (
				45A9FD082E3691E000E27F92 /* wall.app */,
				45A9FD162E3691E000E27F92 /* wallTests.xctest */,
				45A9FD202E3691E000E27F92 /* wallUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		45A9FD072E3691E000E27F92 /* wall */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 45A9FD2A2E3691E000E27F92 /* Build configuration list for PBXNativeTarget "wall" */;
			buildPhases = (
				45A9FD042E3691E000E27F92 /* Sources */,
				45A9FD052E3691E000E27F92 /* Frameworks */,
				45A9FD062E3691E000E27F92 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				45A9FD0A2E3691E000E27F92 /* wall */,
			);
			name = wall;
			packageProductDependencies = (
			);
			productName = wall;
			productReference = 45A9FD082E3691E000E27F92 /* wall.app */;
			productType = "com.apple.product-type.application";
		};
		45A9FD152E3691E000E27F92 /* wallTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 45A9FD2D2E3691E000E27F92 /* Build configuration list for PBXNativeTarget "wallTests" */;
			buildPhases = (
				45A9FD122E3691E000E27F92 /* Sources */,
				45A9FD132E3691E000E27F92 /* Frameworks */,
				45A9FD142E3691E000E27F92 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				45A9FD182E3691E000E27F92 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				45A9FD192E3691E000E27F92 /* wallTests */,
			);
			name = wallTests;
			packageProductDependencies = (
			);
			productName = wallTests;
			productReference = 45A9FD162E3691E000E27F92 /* wallTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		45A9FD1F2E3691E000E27F92 /* wallUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 45A9FD302E3691E000E27F92 /* Build configuration list for PBXNativeTarget "wallUITests" */;
			buildPhases = (
				45A9FD1C2E3691E000E27F92 /* Sources */,
				45A9FD1D2E3691E000E27F92 /* Frameworks */,
				45A9FD1E2E3691E000E27F92 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				45A9FD222E3691E000E27F92 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				45A9FD232E3691E000E27F92 /* wallUITests */,
			);
			name = wallUITests;
			packageProductDependencies = (
			);
			productName = wallUITests;
			productReference = 45A9FD202E3691E000E27F92 /* wallUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		45A9FD002E3691E000E27F92 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					45A9FD072E3691E000E27F92 = {
						CreatedOnToolsVersion = 16.4;
					};
					45A9FD152E3691E000E27F92 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 45A9FD072E3691E000E27F92;
					};
					45A9FD1F2E3691E000E27F92 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 45A9FD072E3691E000E27F92;
					};
				};
			};
			buildConfigurationList = 45A9FD032E3691E000E27F92 /* Build configuration list for PBXProject "wall" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 45A9FCFF2E3691E000E27F92;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 45A9FD092E3691E000E27F92 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				45A9FD072E3691E000E27F92 /* wall */,
				45A9FD152E3691E000E27F92 /* wallTests */,
				45A9FD1F2E3691E000E27F92 /* wallUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		45A9FD062E3691E000E27F92 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		45A9FD142E3691E000E27F92 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		45A9FD1E2E3691E000E27F92 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		45A9FD042E3691E000E27F92 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		45A9FD122E3691E000E27F92 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		45A9FD1C2E3691E000E27F92 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		45A9FD182E3691E000E27F92 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 45A9FD072E3691E000E27F92 /* wall */;
			targetProxy = 45A9FD172E3691E000E27F92 /* PBXContainerItemProxy */;
		};
		45A9FD222E3691E000E27F92 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 45A9FD072E3691E000E27F92 /* wall */;
			targetProxy = 45A9FD212E3691E000E27F92 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		45A9FD282E3691E000E27F92 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		45A9FD292E3691E000E27F92 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		45A9FD2B2E3691E000E27F92 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = wall/wall.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = yasir.wall;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		45A9FD2C2E3691E000E27F92 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = wall/wall.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = yasir.wall;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		45A9FD2E2E3691E000E27F92 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = yasir.wallTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/wall.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/wall";
			};
			name = Debug;
		};
		45A9FD2F2E3691E000E27F92 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = yasir.wallTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/wall.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/wall";
			};
			name = Release;
		};
		45A9FD312E3691E000E27F92 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = yasir.wallUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = wall;
			};
			name = Debug;
		};
		45A9FD322E3691E000E27F92 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = yasir.wallUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = wall;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		45A9FD032E3691E000E27F92 /* Build configuration list for PBXProject "wall" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				45A9FD282E3691E000E27F92 /* Debug */,
				45A9FD292E3691E000E27F92 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		45A9FD2A2E3691E000E27F92 /* Build configuration list for PBXNativeTarget "wall" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				45A9FD2B2E3691E000E27F92 /* Debug */,
				45A9FD2C2E3691E000E27F92 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		45A9FD2D2E3691E000E27F92 /* Build configuration list for PBXNativeTarget "wallTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				45A9FD2E2E3691E000E27F92 /* Debug */,
				45A9FD2F2E3691E000E27F92 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		45A9FD302E3691E000E27F92 /* Build configuration list for PBXNativeTarget "wallUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				45A9FD312E3691E000E27F92 /* Debug */,
				45A9FD322E3691E000E27F92 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 45A9FD002E3691E000E27F92 /* Project object */;
}
